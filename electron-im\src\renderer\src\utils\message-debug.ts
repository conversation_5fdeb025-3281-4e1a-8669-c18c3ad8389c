/**
 * 消息接收调试工具
 * 用于诊断为什么接收新消息后页面列表没有未读提示
 */

import { wsService } from '../services/websocketService'
import { useMessageStore } from '../store/message'
import { apiClient } from '../api'

export class MessageDebugger {
  private messageStore: any
  private originalHandlers: Map<string, Function> = new Map()

  constructor() {
    this.messageStore = useMessageStore()
  }

  /**
   * 开始调试模式
   */
  startDebugging(): void {
    console.log('🔍 开始消息接收调试模式...')
    
    // 保存原始处理器
    this.saveOriginalHandlers()
    
    // 设置调试处理器
    this.setupDebugHandlers()
    
    // 输出当前状态
    this.logCurrentState()
  }

  /**
   * 停止调试模式
   */
  stopDebugging(): void {
    console.log('🔍 停止消息接收调试模式')
    this.restoreOriginalHandlers()
  }

  /**
   * 保存原始事件处理器
   */
  private saveOriginalHandlers(): void {
    // 这里我们需要重新设置事件监听器来进行调试
  }

  /**
   * 设置调试事件处理器
   */
  private setupDebugHandlers(): void {
    // 监听 WebSocket 消息
    wsService.on('onMessage', (message) => {
      console.log('🔍 [DEBUG] 收到 WebSocket 消息:', message)
      console.log('🔍 [DEBUG] 消息详情:')
      console.log('  - ID:', message.id)
      console.log('  - 发送者ID:', message.senderId)
      console.log('  - 接收者ID:', message.receiverId)
      console.log('  - 内容:', message.content)
      console.log('  - 时间戳:', message.timestamp)
      
      // 检查当前用户ID
      const currentUserId = this.getCurrentUserId()
      console.log('🔍 [DEBUG] 当前用户ID:', currentUserId)
      
      // 检查聊天用户ID计算
      const chatUserId = message.senderId === currentUserId ? message.receiverId : message.senderId
      console.log('🔍 [DEBUG] 计算的聊天用户ID:', chatUserId)
      
      // 检查当前聊天用户
      console.log('🔍 [DEBUG] 当前聊天用户ID:', this.messageStore.currentChatUserId)
      
      // 检查是否应该增加未读计数
      const shouldIncreaseUnread = chatUserId !== this.messageStore.currentChatUserId && message.senderId !== currentUserId
      console.log('🔍 [DEBUG] 是否应该增加未读计数:', shouldIncreaseUnread)
      console.log('  - 聊天用户不是当前用户:', chatUserId !== this.messageStore.currentChatUserId)
      console.log('  - 消息不是自己发送的:', message.senderId !== currentUserId)
      
      // 检查会话更新前的状态
      const existingSession = this.messageStore.chatSessions.get(chatUserId)
      console.log('🔍 [DEBUG] 更新前的会话状态:', existingSession)
    })

    wsService.on('onStateChange', (state) => {
      console.log('🔍 [DEBUG] WebSocket 状态变化:', state)
    })

    wsService.on('onError', (error) => {
      console.log('🔍 [DEBUG] WebSocket 错误:', error)
    })
  }

  /**
   * 恢复原始事件处理器
   */
  private restoreOriginalHandlers(): void {
    // 这里需要重新初始化 WebSocket 事件监听器
    console.log('🔍 [DEBUG] 恢复原始事件处理器')
  }

  /**
   * 输出当前状态
   */
  private logCurrentState(): void {
    console.log('🔍 [DEBUG] 当前状态快照:')
    console.log('  - WebSocket 连接状态:', wsService.getState())
    console.log('  - 当前用户ID:', this.getCurrentUserId())
    console.log('  - 当前聊天用户ID:', this.messageStore.currentChatUserId)
    console.log('  - 聊天会话数量:', this.messageStore.chatSessions.size)
    console.log('  - 总未读数量:', this.messageStore.totalUnreadCount)
    
    // 输出所有聊天会话
    console.log('🔍 [DEBUG] 所有聊天会话:')
    this.messageStore.chatSessions.forEach((session: any, userId: string) => {
      console.log(`  - ${userId}: 未读数量=${session.unreadCount}, 最后消息=${session.lastMessage?.content || '无'}`)
    })
  }

  /**
   * 获取当前用户ID
   */
  private getCurrentUserId(): string {
    const user = apiClient.getCurrentUser()
    return user?.id || ''
  }

  /**
   * 模拟接收消息进行测试
   */
  simulateIncomingMessage(senderId: string, content: string): void {
    console.log('🔍 [DEBUG] 模拟接收消息...')
    
    const mockMessage = {
      id: `test_${Date.now()}`,
      senderId,
      receiverId: this.getCurrentUserId(),
      content,
      timestamp: Date.now()
    }
    
    console.log('🔍 [DEBUG] 模拟消息:', mockMessage)
    
    // 触发消息处理
    wsService['emit']('onMessage', mockMessage)
  }

  /**
   * 检查未读计数逻辑
   */
  checkUnreadLogic(senderId: string): void {
    console.log('🔍 [DEBUG] 检查未读计数逻辑...')
    
    const currentUserId = this.getCurrentUserId()
    const currentChatUserId = this.messageStore.currentChatUserId
    
    console.log('检查条件:')
    console.log('  - 发送者ID:', senderId)
    console.log('  - 当前用户ID:', currentUserId)
    console.log('  - 当前聊天用户ID:', currentChatUserId)
    
    // 计算聊天用户ID
    const chatUserId = senderId === currentUserId ? 'unknown' : senderId
    console.log('  - 计算的聊天用户ID:', chatUserId)
    
    // 检查条件
    const condition1 = chatUserId !== currentChatUserId
    const condition2 = senderId !== currentUserId
    
    console.log('未读计数条件:')
    console.log('  - 聊天用户不是当前用户:', condition1)
    console.log('  - 消息不是自己发送的:', condition2)
    console.log('  - 最终结果:', condition1 && condition2)
  }

  /**
   * 强制刷新聊天会话
   */
  forceRefreshSessions(): void {
    console.log('🔍 [DEBUG] 强制刷新聊天会话...')
    
    // 触发响应式更新
    const sessions = this.messageStore.chatSessions
    this.messageStore.chatSessions = new Map(sessions)
    
    console.log('🔍 [DEBUG] 会话已刷新')
    this.logCurrentState()
  }

  /**
   * 检查 Vue 响应式状态
   */
  checkReactivity(): void {
    console.log('🔍 [DEBUG] 检查 Vue 响应式状态...')
    
    // 检查 chatSessions 是否是响应式的
    console.log('  - chatSessions 类型:', typeof this.messageStore.chatSessions)
    console.log('  - chatSessions 是否是 Map:', this.messageStore.chatSessions instanceof Map)
    console.log('  - sortedChatSessions 长度:', this.messageStore.sortedChatSessions.length)
    console.log('  - totalUnreadCount:', this.messageStore.totalUnreadCount)
  }
}

// 创建全局调试实例
export const messageDebugger = new MessageDebugger()

// 在浏览器控制台中使用的便捷函数
if (typeof window !== 'undefined') {
  // @ts-ignore
  window.debugMessage = messageDebugger
  // @ts-ignore
  window.startMessageDebug = () => messageDebugger.startDebugging()
  // @ts-ignore
  window.stopMessageDebug = () => messageDebugger.stopDebugging()
  // @ts-ignore
  window.simulateMessage = (senderId: string, content: string) => 
    messageDebugger.simulateIncomingMessage(senderId, content)
  // @ts-ignore
  window.checkUnread = (senderId: string) => messageDebugger.checkUnreadLogic(senderId)
  // @ts-ignore
  window.refreshSessions = () => messageDebugger.forceRefreshSessions()
  // @ts-ignore
  window.checkReactivity = () => messageDebugger.checkReactivity()
  
  console.log('🔧 消息调试工具已加载:')
  console.log('  - startMessageDebug() - 开始调试')
  console.log('  - stopMessageDebug() - 停止调试')
  console.log('  - simulateMessage(senderId, content) - 模拟消息')
  console.log('  - checkUnread(senderId) - 检查未读逻辑')
  console.log('  - refreshSessions() - 刷新会话')
  console.log('  - checkReactivity() - 检查响应式状态')
}
