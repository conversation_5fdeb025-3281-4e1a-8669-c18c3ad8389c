<template>
  <div class="debug-panel">
    <h3>消息接收调试面板</h3>
    
    <div class="debug-section">
      <h4>连接状态</h4>
      <div class="status-info">
        <div>WebSocket状态: <span :class="wsStateClass">{{ wsState }}</span></div>
        <div>当前用户ID: <span class="user-id">{{ currentUserId }}</span></div>
        <div>当前聊天用户ID: <span class="user-id">{{ currentChatUserId }}</span></div>
      </div>
    </div>

    <div class="debug-section">
      <h4>聊天会话 ({{ chatSessions.length }})</h4>
      <div class="sessions-list">
        <div v-for="session in chatSessions" :key="session.userId" class="session-item">
          <div class="session-info">
            <span class="session-name">{{ session.userName }}</span>
            <span class="session-id">({{ session.userId }})</span>
            <span class="unread-count" :class="{ 'has-unread': session.unreadCount > 0 }">
              未读: {{ session.unreadCount }}
            </span>
          </div>
          <div class="last-message">
            最后消息: {{ session.lastMessage?.content || '无' }}
          </div>
        </div>
      </div>
    </div>

    <div class="debug-section">
      <h4>总未读数量: <span class="total-unread">{{ totalUnreadCount }}</span></h4>
    </div>

    <div class="debug-section">
      <h4>模拟消息测试</h4>
      <div class="test-controls">
        <input
          v-model="testSenderId"
          placeholder="发送者ID"
          class="test-input"
        />
        <input
          v-model="testContent"
          placeholder="消息内容"
          class="test-input"
        />
        <button @click="simulateMessage" class="test-btn">模拟接收消息</button>
      </div>
    </div>

    <div class="debug-section">
      <h4>调试操作</h4>
      <div class="debug-actions">
        <button @click="refreshData" class="debug-btn">刷新数据</button>
        <button @click="clearUnread" class="debug-btn">清除所有未读</button>
        <button @click="logState" class="debug-btn">输出状态到控制台</button>
      </div>
    </div>

    <div class="debug-section">
      <h4>最近日志</h4>
      <div class="log-container">
        <div v-for="(log, index) in recentLogs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useMessageStore } from '../store/message'
import { wsService } from '../services/websocketService'
import { apiClient } from '../api'

const messageStore = useMessageStore()

// 响应式数据
const testSenderId = ref('test-sender-123')
const testContent = ref('这是一条测试消息')
const recentLogs = ref<Array<{ time: string; message: string }>>([])

// 计算属性
const wsState = computed(() => wsService.getState())
const currentUserId = computed(() => apiClient.getCurrentUser()?.id || '')
const currentChatUserId = computed(() => messageStore.currentChatUserId)
const chatSessions = computed(() => messageStore.sortedChatSessions)
const totalUnreadCount = computed(() => messageStore.totalUnreadCount)

const wsStateClass = computed(() => {
  switch (wsState.value) {
    case 'CONNECTED': return 'status-connected'
    case 'CONNECTING': return 'status-connecting'
    case 'DISCONNECTED': return 'status-disconnected'
    case 'ERROR': return 'status-error'
    default: return 'status-unknown'
  }
})

// 添加日志
function addLog(message: string) {
  const time = new Date().toLocaleTimeString()
  recentLogs.value.unshift({ time, message })
  if (recentLogs.value.length > 10) {
    recentLogs.value.pop()
  }
}

// 模拟接收消息
function simulateMessage() {
  if (!testSenderId.value || !testContent.value) {
    addLog('❌ 请填写发送者ID和消息内容')
    return
  }

  const mockMessage = {
    id: `test_${Date.now()}`,
    senderId: testSenderId.value,
    receiverId: currentUserId.value,
    content: testContent.value,
    timestamp: Date.now()
  }

  addLog(`📤 模拟消息: ${testSenderId.value} -> ${mockMessage.content}`)
  
  // 直接调用消息处理函数
  messageStore['handleIncomingMessage'](mockMessage)
  
  // 清空输入
  testContent.value = ''
}

// 刷新数据
function refreshData() {
  addLog('🔄 刷新数据')
  // 强制触发响应式更新
  messageStore.chatSessions = new Map(messageStore.chatSessions)
}

// 清除所有未读
function clearUnread() {
  addLog('🧹 清除所有未读计数')
  messageStore.chatSessions.forEach((session) => {
    session.unreadCount = 0
  })
  refreshData()
}

// 输出状态到控制台
function logState() {
  console.log('🔍 [DebugPanel] 当前状态:')
  console.log('  - WebSocket状态:', wsState.value)
  console.log('  - 当前用户ID:', currentUserId.value)
  console.log('  - 当前聊天用户ID:', currentChatUserId.value)
  console.log('  - 聊天会话数量:', chatSessions.value.length)
  console.log('  - 总未读数量:', totalUnreadCount.value)
  console.log('  - 所有会话:', chatSessions.value)
  addLog('📊 状态已输出到控制台')
}

// 监听 WebSocket 事件
let eventListeners: Array<() => void> = []

onMounted(() => {
  addLog('🚀 调试面板已加载')
  
  // 设置事件监听器
  const onMessage = (message: any) => {
    addLog(`📥 收到消息: ${message.senderId} -> ${message.content}`)
  }
  
  const onStateChange = (state: string) => {
    addLog(`🔄 连接状态: ${state}`)
  }
  
  wsService.on('onMessage', onMessage)
  wsService.on('onStateChange', onStateChange)
  
  // 保存清理函数
  eventListeners.push(
    () => wsService.off('onMessage'),
    () => wsService.off('onStateChange')
  )
})

onUnmounted(() => {
  // 清理事件监听器
  eventListeners.forEach(cleanup => cleanup())
})
</script>

<style scoped>
.debug-panel {
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
  font-family: monospace;
  max-width: 800px;
  margin: 20px auto;
}

.debug-section {
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 6px;
  border: 1px solid #ddd;
}

.debug-section h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.status-info div {
  margin: 5px 0;
}

.status-connected { color: #28a745; font-weight: bold; }
.status-connecting { color: #ffc107; font-weight: bold; }
.status-disconnected { color: #6c757d; font-weight: bold; }
.status-error { color: #dc3545; font-weight: bold; }

.user-id {
  font-weight: bold;
  color: #007bff;
}

.sessions-list {
  max-height: 200px;
  overflow-y: auto;
}

.session-item {
  padding: 8px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 8px;
}

.session-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.session-name {
  font-weight: bold;
}

.session-id {
  color: #666;
  font-size: 12px;
}

.unread-count {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  background: #f8f9fa;
}

.unread-count.has-unread {
  background: #dc3545;
  color: white;
}

.last-message {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.total-unread {
  font-size: 24px;
  font-weight: bold;
  color: #dc3545;
}

.test-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.test-input {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  flex: 1;
}

.test-btn, .debug-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.test-btn {
  background: #007bff;
  color: white;
}

.debug-actions {
  display: flex;
  gap: 10px;
}

.debug-btn {
  background: #6c757d;
  color: white;
}

.log-container {
  max-height: 150px;
  overflow-y: auto;
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  margin-bottom: 4px;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 8px;
}

.log-message {
  color: #333;
}
</style>
