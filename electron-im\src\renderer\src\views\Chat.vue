<!-- 聊天主页 -->
<template>
  <div class="flex h-screen overflow-hidden bg-gray-50 font-sans">
    <!-- 左侧聊天列表 -->
    <ChatSidebar
      :contacts="chats"
      :current-contact-id="currentChatId"
      :is-loading="isLoadingUsers"
      :loading-text="loadingStatus"
      @select-contact="selectChat"
      @refresh="loadChatContacts"
    />

    <!-- 右侧聊天区域 -->
    <div class="flex-1 flex flex-col">
      <!-- 聊天头部 -->
      <ChatHeader
        :current-contact="currentChat"
        @more-options="showMoreOptions"
        @logout="handleLogout"
      />

      <!-- 消息列表 -->
      <MessageList
        ref="messageListRef"
        :messages="currentMessages"
        :current-contact="currentChat"
      />

      <!-- 消息输入 -->
      <MessageInput
        :current-contact="currentChat"
        @send-message="sendMessage"
        @insert-emoji="insertEmoji"
        @attach-file="attachFile"
        @insert-image="insertImage"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useUserStore } from '../store/user'
import { useMessageStore } from '../store/message'
import { apiClient } from '../api'
import type { User } from '../api'
import ChatSidebar from '../components/ChatSidebar.vue'
import ChatHeader from '../components/ChatHeader.vue'
import MessageList from '../components/MessageList.vue'
import MessageInput from '../components/MessageInput.vue'

// 用户状态管理
const userStore = useUserStore()
const messageStore = useMessageStore()

// 响应式数据
const currentChatId = ref<string | null>(null)
const isLoadingUsers = ref(false)
const loadingStatus = ref('')
const messageListRef = ref<InstanceType<typeof MessageList>>()

// 聊天数据 - 从聊天联系人API获取有聊天记录的联系人
const chats = ref<
  Array<{
    id: string
    name: string
    avatar: string
    status: string
    lastMessage: string
    lastMessageTime: Date
    unreadCount: number
    user: User
  }>
>([])

// 从API加载所有用户列表
const loadChatContacts = async () => {
  try {
    isLoadingUsers.value = true
    loadingStatus.value = '正在获取用户列表...'

    const usersResponse = await apiClient.getUsers()
    if (!usersResponse.success) {
      throw new Error('获取用户列表失败')
    }

    // 过滤掉当前用户，转换为聊天列表格式
    chats.value = usersResponse.users
      .filter((user) => user.id !== userStore.currentUser.value?.id)
      .map((user) => ({
        id: user.id,
        name: user.displayName,
        avatar: getNameAvatar(user.displayName),
        status: user.isOnline ? '在线' : '离线',
        lastMessage: '点击开始聊天',
        lastMessageTime: new Date(user.lastOnlineTime),
        unreadCount: 0,
        user: user
      }))

    console.log('加载用户列表成功:', chats.value)
    loadingStatus.value = `加载完成！共 ${chats.value.length} 个联系人`
  } catch (error) {
    console.error('加载用户列表失败:', error)
    loadingStatus.value = '加载失败'
    // 如果API失败，使用默认数据
    chats.value = [
      {
        id: '689da0e4467567db78ae889d',
        name: '陈颢文',
        avatar: getNameAvatar('陈颢文'),
        status: '在线',
        lastMessage: '点击开始聊天',
        lastMessageTime: new Date(Date.now() - 1000 * 60 * 5),
        unreadCount: 0,
        user: {
          id: '689da0e4467567db78ae889d',
          username: 'chenhaowen',
          email: '<EMAIL>',
          displayName: '陈颢文',
          avatar: '/avatars/default.png',
          isOnline: true,
          lastOnlineTime: '2025-08-14T08:42:27.539Z'
        }
      }
    ]
  } finally {
    isLoadingUsers.value = false
    // 清空加载状态
    setTimeout(() => {
      loadingStatus.value = ''
    }, 1000)
  }
}

interface Message {
  id: string
  senderId: string
  senderName: string
  content: string
  timestamp: Date
}

// 计算属性
const currentChat = computed(() => {
  const chat = chats.value.find((chat) => chat.id === currentChatId.value)
  console.log(`当前聊天联系人:`, chat)
  console.log(`当前聊天ID: ${currentChatId.value}`)
  console.log(
    `联系人列表:`,
    chats.value.map((c) => ({ id: c.id, name: c.name }))
  )
  return chat
})

// 直接使用messageStore的currentMessages，并转换格式
const currentMessages = computed(() => {
  const storeMessages = messageStore.currentMessages
  console.log(`当前聊天ID: ${currentChatId.value}`)
  console.log(`messageStore中的消息数量: ${storeMessages.length}`)
  console.log(`messageStore中的消息:`, storeMessages)

  // 转换为组件需要的格式
  const transformedMessages = storeMessages.map((msg) => ({
    id: msg.id,
    senderId: msg.senderId,
    senderName: getSenderName(msg.senderId),
    content: msg.content,
    timestamp: new Date(msg.timestamp)
  }))

  console.log(`转换后的消息:`, transformedMessages)
  return transformedMessages
})

// 获取发送者姓名
const getSenderName = (senderId: string) => {
  const currentUserId = userStore.currentUser.value?.id
  console.log(`获取发送者姓名 - senderId: ${senderId}, currentUserId: ${currentUserId}`)

  if (senderId === currentUserId) {
    const name = userStore.userDisplayName.value || '我'
    console.log(`当前用户消息，显示名称: ${name}`)
    return name
  }

  // 从聊天联系人中查找
  const contact = chats.value.find((chat) => chat.user.id === senderId)
  const name = contact?.user.displayName || '未知用户'
  console.log(`其他用户消息，senderId: ${senderId}, 显示名称: ${name}`)
  return name
}

// 处理收到新消息时的联系人列表更新
const handleNewMessage = (senderId: string, content: string, timestamp: number) => {
  // 查找发送者在联系人列表中的位置
  const senderIndex = chats.value.findIndex((chat) => chat.id === senderId)

  if (senderIndex !== -1) {
    const sender = chats.value[senderIndex]

    // 更新最后消息和时间
    sender.lastMessage = content
    sender.lastMessageTime = new Date(timestamp)

    // 如果不是当前聊天用户，增加未读计数
    if (currentChatId.value !== senderId) {
      sender.unreadCount = (sender.unreadCount || 0) + 1
    }

    // 将该联系人移到列表顶部
    chats.value.splice(senderIndex, 1)
    chats.value.unshift(sender)

    console.log(`收到来自 ${sender.name} 的新消息，未读数: ${sender.unreadCount}`)
  }
}

// 清除未读计数
const clearUnreadCount = (userId: string) => {
  const contact = chats.value.find((chat) => chat.id === userId)
  if (contact && contact.unreadCount > 0) {
    contact.unreadCount = 0
    console.log(`清除用户 ${contact.name} 的未读计数`)
  }
}

// 同步messageStore中的chatSessions到联系人列表
const syncChatSessionsToContacts = () => {
  const sessions = messageStore.chatSessions

  sessions.forEach((session, userId) => {
    const contactIndex = chats.value.findIndex((chat) => chat.id === userId)

    if (contactIndex !== -1) {
      const contact = chats.value[contactIndex]

      // 更新联系人信息
      if (session.lastMessage) {
        contact.lastMessage = session.lastMessage.content
        contact.lastMessageTime = new Date(session.lastMessage.timestamp)
      }

      // 更新未读计数（如果不是当前聊天用户）
      if (currentChatId.value !== userId) {
        contact.unreadCount = session.unreadCount
      } else {
        contact.unreadCount = 0
      }

      // 如果有新消息，将联系人移到顶部
      if (session.unreadCount > 0 && currentChatId.value !== userId) {
        chats.value.splice(contactIndex, 1)
        chats.value.unshift(contact)
        console.log(`${contact.name} 有新消息，移到列表顶部`)
      }
    }
  })
}

// 组件挂载时的初始化
onMounted(async () => {
  // 检查用户是否已登录
  if (!userStore.isAuthenticated.value) {
    console.log('用户未登录，应该跳转到登录页')
    return
  }

  // 初始化WebSocket连接
  await messageStore.initWebSocket()

  // 加载聊天联系人列表
  await loadChatContacts()

  // 使用watch监听messageStore中的chatSessions变化
  watch(
    () => messageStore.chatSessions,
    (newSessions, oldSessions) => {
      console.log('检测到chatSessions变化，同步联系人列表')
      console.log('新的sessions:', Array.from(newSessions.entries()))
      syncChatSessionsToContacts()
    },
    { deep: true }
  )
})

// 工具函数
const getNameAvatar = (name: string) => {
  // 获取姓名的后两个字作为头像
  return name.length >= 2 ? name.slice(-2) : name
}

// 方法
const selectChat = async (chatId: string) => {
  console.log(`=== 选择聊天开始 ===`)
  console.log(`选择聊天: ${chatId}`)

  // 设置当前聊天ID
  currentChatId.value = chatId
  console.log(`设置currentChatId: ${currentChatId.value}`)

  // 设置messageStore的当前聊天用户
  messageStore.setCurrentChatUser(chatId)
  console.log(`设置messageStore currentChatUserId: ${messageStore.currentChatUserId}`)

  // 清除该联系人的未读计数
  clearUnreadCount(chatId)

  // 自动加载聊天历史
  console.log(`开始加载聊天历史: ${chatId}`)
  const success = await messageStore.loadChatHistory(chatId, 1, 50)
  console.log(`聊天历史加载结果: ${success}`)

  // 检查加载后的消息
  const messages = messageStore.currentMessages
  console.log(`当前消息数量: ${messages.length}`, messages)

  // 检查messages Map中的数据
  console.log(`messages Map中的所有数据:`, Array.from(messageStore.messages.entries()))
  console.log(`=== 选择聊天结束 ===`)
}

const showMoreOptions = () => {
  console.log('显示更多选项')
  // TODO: 实现更多选项功能
}

const handleLogout = () => {
  userStore.logout()
  messageStore.disconnectWebSocket()
  // 触发父组件重新渲染，显示登录页面
  window.location.reload()
}

const insertEmoji = () => {
  console.log('插入表情')
  // TODO: 实现表情选择功能
}

const attachFile = () => {
  console.log('附加文件')
  // TODO: 实现文件上传功能
}

const insertImage = () => {
  console.log('插入图片')
  // TODO: 实现图片上传功能
}

const sendMessage = async (content: string) => {
  if (!content.trim() || !currentChatId.value) {
    return
  }

  try {
    // 使用messageStore发送消息
    await messageStore.sendMessage(currentChatId.value, content)

    // 更新聊天列表中的最后消息
    const chat = chats.value.find((c) => c.id === currentChatId.value)
    if (chat) {
      chat.lastMessage = content
      chat.lastMessageTime = new Date()
    }
  } catch (error) {
    console.error('发送消息失败:', error)
  }
}
</script>
