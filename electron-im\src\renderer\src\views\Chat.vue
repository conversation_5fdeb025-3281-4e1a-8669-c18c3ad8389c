<!-- 聊天主页 -->
<template>
  <div class="flex h-screen overflow-hidden bg-gray-50 font-sans">
    <!-- 左侧聊天列表 -->
    <ChatSidebar
      :contacts="chats"
      :current-contact-id="currentChatId"
      :is-loading="isLoadingUsers"
      :loading-text="loadingStatus"
      @select-contact="selectChat"
      @refresh="loadChatContacts"
    />

    <!-- 右侧聊天区域 -->
    <div class="flex-1 flex flex-col">
      <!-- 聊天头部 -->
      <ChatHeader
        :current-contact="currentChat"
        @more-options="showMoreOptions"
        @logout="handleLogout"
      />
      <svg
        data-v-05eee853=""
        viewBox="0 0 176 176"
        fill="none"
        aria-hidden="true"
        class="svg-icon"
        style="width: 176px; height: 176px"
      >
        <path
          pid="0"
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M95.749 121.494c12.483-5.163 21.171-16.671 21.171-30.04 0-18.19-16.088-32.938-35.933-32.938S45.055 73.263 45.055 91.455c0 12.191 7.226 22.836 17.966 28.531v12.127c0 1.993 2.06 3.321 3.876 2.495l28.852-13.114Z"
          fill="url(#svgiconid_no-chats_a_18_2TP5E)"
        ></path>
        <path
          pid="1"
          d="m95.749 121.494-.105-.254-.008.004.113.25Zm-32.728-1.508h.274v-.165l-.146-.077-.128.242Zm3.876 14.622.113.25-.113-.25Zm49.749-43.153c0 13.243-8.607 24.66-21.002 29.786l.21.506c12.571-5.199 21.34-16.799 21.34-30.292h-.548ZM80.987 58.79c19.717 0 35.659 14.647 35.659 32.665h.548c0-18.365-16.233-33.213-36.207-33.213v.548ZM45.33 91.455c0-18.018 15.941-32.665 35.658-32.665v-.548c-19.973 0-36.207 14.848-36.207 33.213h.548Zm17.82 28.289c-10.662-5.654-17.82-16.211-17.82-28.29h-.549c0 12.306 7.294 23.038 18.112 28.774l.257-.484Zm.146 12.369v-12.127h-.548v12.127h.548Zm3.489 2.246c-1.634.743-3.49-.452-3.49-2.246h-.547c0 2.193 2.267 3.652 4.263 2.745l-.227-.499Zm28.852-13.115-28.852 13.115.226.499 28.852-13.115-.226-.499Z"
          fill="url(#svgiconid_no-chats_b_18_2TP5E)"
        ></path>
        <path
          pid="2"
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M116.92 91.455c0 13.367-8.687 24.875-21.168 30.038l-7.498 3.408c-1.89-3.376-2.955-7.203-2.955-11.258 0-13.643 12.066-24.703 26.949-24.703 1.567 0 3.102.122 4.596.357.05.714.076 1.433.076 2.158Z"
          fill="url(#svgiconid_no-chats_c_18_2TP5E)"
        ></path>
        <path
          pid="3"
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M102.853 135.693c-9.361-3.872-15.877-12.503-15.877-22.529 0-13.644 12.066-24.704 26.949-24.704 14.884 0 26.95 11.06 26.95 24.704 0 9.144-5.42 17.127-13.475 21.399v9.094a2.056 2.056 0 0 1-2.907 1.872l-21.64-9.836Z"
          fill="url(#svgiconid_no-chats_d_18_2TP5E)"
        ></path>
        <path
          pid="4"
          d="m102.853 135.693.101-.243.008.003-.109.24Zm24.547-1.13h-.263v-.159l.14-.074.123.233Zm-2.907 10.966-.109.24.109-.24Zm-37.254-32.365c0 9.906 6.438 18.449 15.715 22.286l-.202.486c-9.446-3.907-16.04-12.626-16.04-22.772h.527Zm26.686-24.44c-14.76 0-26.686 10.963-26.686 24.44h-.526c0-13.81 12.205-24.967 27.212-24.967v.526Zm26.687 24.44c0-13.477-11.926-24.44-26.687-24.44v-.527c15.007 0 27.213 11.157 27.213 24.967h-.526Zm-13.335 21.166c7.98-4.232 13.335-12.132 13.335-21.166h.526c0 9.253-5.484 17.32-13.615 21.631l-.246-.465Zm-.14 9.327v-9.094h.526v9.094h-.526Zm-2.535 1.633a1.794 1.794 0 0 0 2.535-1.633h.526c0 1.687-1.743 2.81-3.279 2.112l.218-.479Zm-21.64-9.837 21.64 9.837-.218.479-21.64-9.836.218-.48Z"
          fill="url(#svgiconid_no-chats_e_18_2TP5E)"
        ></path>
        <path
          pid="5"
          d="M104.583 112.987c0 1.665-1.394 3.014-3.114 3.014s-3.114-1.349-3.114-3.014c0-1.664 1.394-3.014 3.114-3.014s3.114 1.35 3.114 3.014ZM117.039 112.987c0 1.665-1.394 3.014-3.114 3.014s-3.114-1.349-3.114-3.014c0-1.664 1.394-3.014 3.114-3.014s3.114 1.35 3.114 3.014ZM129.496 112.987c0 1.665-1.394 3.014-3.114 3.014s-3.114-1.349-3.114-3.014c0-1.664 1.394-3.014 3.114-3.014s3.114 1.35 3.114 3.014Z"
          fill="#000"
          fill-opacity=".25"
        ></path>
        <path
          pid="6"
          d="M105.386 112.385a3.013 3.013 0 1 1-6.026 0 3.013 3.013 0 0 1 6.026 0ZM117.441 112.385a3.013 3.013 0 1 1-6.026 0 3.013 3.013 0 0 1 6.026 0ZM129.496 112.385a3.013 3.013 0 1 1-6.027 0 3.013 3.013 0 0 1 6.027 0Z"
          fill="#EDF7FF"
        ></path>
        <g filter="url(#svgiconid_no-chats_f_18_2TP5E)">
          <mask id="svgiconid_no-chats_i_18_2TP5E" fill="#fff">
            <path
              pid="7"
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M133.783 33.79c-5.809 0-10.518 4.709-10.518 10.517v19.754c0 5.657 4.467 10.271 10.066 10.508v5.07l8.167-5.06h9.328c5.809 0 10.518-4.71 10.518-10.518V44.307c0-5.809-4.709-10.517-10.518-10.517h-17.043Z"
            ></path>
          </mask>
          <path
            pid="8"
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M133.783 33.79c-5.809 0-10.518 4.709-10.518 10.517v19.754c0 5.657 4.467 10.271 10.066 10.508v5.07l8.167-5.06h9.328c5.809 0 10.518-4.71 10.518-10.518V44.307c0-5.809-4.709-10.517-10.518-10.517h-17.043Z"
            fill="url(#svgiconid_no-chats_g_18_2TP5E)"
          ></path>
          <path
            pid="9"
            d="M133.331 74.569h.502v-.482l-.481-.02-.021.502Zm0 5.07h-.502v.903l.767-.475-.265-.427Zm8.167-5.06v-.503h-.143l-.122.075.265.427Zm-17.73-30.272c0-5.531 4.484-10.015 10.015-10.015v-1.005c-6.086 0-11.02 4.934-11.02 11.02h1.005Zm0 19.754V44.307h-1.005v19.754h1.005Zm9.584 10.006c-5.331-.225-9.584-4.62-9.584-10.006h-1.005c0 5.927 4.68 10.761 10.547 11.01l.042-1.004Zm.481 5.573v-5.071h-1.004v5.07h1.004Zm7.4-5.489-8.167 5.062.53.854 8.166-5.062-.529-.854Zm9.593-.075h-9.328v1.004h9.328v-1.004Zm10.015-10.015c0 5.531-4.484 10.015-10.015 10.015v1.004c6.086 0 11.02-4.933 11.02-11.02h-1.005Zm0-19.754v19.754h1.005V44.307h-1.005Zm-10.015-10.015c5.531 0 10.015 4.484 10.015 10.015h1.005c0-6.086-4.934-11.02-11.02-11.02v1.005Zm-17.043 0h17.043v-1.005h-17.043v1.005Z"
            fill="url(#svgiconid_no-chats_h_18_2TP5E)"
            mask="url(#svgiconid_no-chats_i_18_2TP5E)"
          ></path>
        </g>
        <path
          pid="10"
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M146.077 41.496c-.213-.646-1.127-.646-1.34 0l-1.968 5.985a.705.705 0 0 1-.45.45l-5.985 1.968c-.646.213-.646 1.127 0 1.34l5.985 1.968c.*************.45.45l1.968 5.985c.213.646 1.127.646 1.34 0l1.968-5.986a.705.705 0 0 1 .45-.45l5.985-1.968c.646-.212.646-1.126 0-1.339l-5.985-1.968a.705.705 0 0 1-.45-.45l-1.968-5.985Zm-10.149 14.32a.502.502 0 0 0-.954 0l-1.132 3.44a.501.501 0 0 1-.32.321l-3.441 1.132a.502.502 0 0 0 0 .954l3.441 1.132c.*************.32.32l1.132 3.44c.151.461.803.461.954 0l1.132-3.44a.503.503 0 0 1 .32-.32l3.441-1.132a.502.502 0 0 0 0-.954l-3.441-1.132a.503.503 0 0 1-.32-.32l-1.132-3.441Z"
          fill="#fff"
        ></path>
        <path
          pid="11"
          d="m144.737 41.496-.167-.055.167.055Zm1.34 0-.168.055.168-.055Zm-3.308 5.985.167.055-.167-.055Zm-.45.45-.055-.168.055.168Zm-5.985 1.968-.055-.167.055.167Zm0 1.34-.055.167.055-.168Zm5.985 1.968-.055.167.055-.167Zm.45.45.167-.056-.167.055Zm1.968 5.985-.167.055.167-.055Zm1.34 0-.168-.055.168.055Zm1.968-5.986.168.055-.168-.055Zm.45-.45.055.168-.055-.167Zm5.985-1.968.055.168-.055-.168Zm0-1.339.055-.167-.055.167Zm-5.985-1.968.055-.168-.055.168Zm-.45-.45.168-.055-.168.055Zm-13.071 8.335-.167-.055.167.055Zm.954 0 .168-.055-.168.055Zm-2.086 3.44.168.056-.168-.055Zm-.32.321-.055-.167.055.167Zm-3.441 1.132-.055-.168.055.168Zm0 .954-.055.167.055-.167Zm3.441 1.132.055-.168-.055.168Zm.32.32.168-.055-.168.055Zm1.132 3.44.167-.054-.167.055Zm.954 0 .168.056-.168-.055Zm1.132-3.44.167.055-.167-.055Zm.32-.32-.055-.168.055.168Zm3.441-1.132.055.167-.055-.167Zm0-.954.055-.168-.055.168Zm-3.441-1.132.055-.167-.055.167Zm-.32-.32.167-.055-.167.055Zm7.845-17.706a.528.528 0 0 1 1.004 0l.335-.11c-.266-.808-1.408-.808-1.674 0l.335.11Zm-1.969 5.985 1.969-5.985-.335-.11-1.969 5.985.335.11Zm-.561.562a.88.88 0 0 0 .561-.562l-.335-.11a.527.527 0 0 1-.337.337l.111.335Zm-5.986 1.969 5.986-1.969-.111-.335-5.985 1.969.11.335Zm0 1.004a.529.529 0 0 1 0-1.004l-.11-.335c-.808.265-.808 1.408 0 1.674l.11-.335Zm5.986 1.968-5.986-1.968-.11.335 5.985 1.968.111-.335Zm.561.562a.88.88 0 0 0-.561-.562l-.111.335c.16.053.285.178.337.337l.335-.11Zm1.969 5.986-1.969-5.986-.335.11 1.969 5.986.335-.11Zm1.004 0a.528.528 0 0 1-1.004 0l-.335.11c.266.807 1.408.807 1.674 0l-.335-.11Zm1.969-5.986-1.969 5.986.335.11 1.969-5.986-.335-.11Zm.561-.562a.88.88 0 0 0-.561.562l.335.11a.527.527 0 0 1 .337-.337l-.111-.335Zm5.986-1.968-5.986 1.968.111.335 5.985-1.968-.11-.335Zm0-1.004a.529.529 0 0 1 0 1.004l.11.335c.808-.266.808-1.409 0-1.674l-.11.335Zm-5.986-1.969 5.986 1.969.11-.335-5.985-1.969-.111.335Zm-.561-.562a.88.88 0 0 0 .561.562l.111-.335a.527.527 0 0 1-.337-.337l-.335.11Zm-1.969-5.985 1.969 5.985.335-.11-1.969-5.985-.335.11Zm-10.768 14.32a.326.326 0 0 1 .62 0l.335-.11c-.205-.622-1.085-.622-1.289 0l.334.11Zm-1.131 3.44 1.131-3.44-.334-.11-1.132 3.44.335.11Zm-.433.433a.68.68 0 0 0 .433-.432l-.335-.11a.326.326 0 0 1-.208.208l.11.334Zm-3.441 1.132 3.441-1.132-.11-.334-3.441 1.131.11.335Zm0 .62a.326.326 0 0 1 0-.62l-.11-.335c-.622.205-.622 1.085 0 1.29l.11-.335Zm3.441 1.131-3.441-1.131-.11.334 3.441 1.132.11-.335Zm.433.433a.68.68 0 0 0-.433-.433l-.11.335c.098.032.176.11.208.208l.335-.11Zm1.131 3.44-1.131-3.44-.335.11 1.132 3.441.334-.11Zm.62 0a.326.326 0 0 1-.62 0l-.334.111c.204.622 1.084.622 1.289 0l-.335-.11Zm1.132-3.44-1.132 3.44.335.111 1.131-3.441-.334-.11Zm.432-.433a.676.676 0 0 0-.432.433l.334.11a.328.328 0 0 1 .208-.208l-.11-.335Zm3.441-1.131-3.441 1.131.11.335 3.441-1.132-.11-.334Zm0-.62a.326.326 0 0 1 0 .62l.11.334c.622-.204.622-1.084 0-1.289l-.11.335Zm-3.441-1.132 3.441 1.132.11-.335-3.441-1.131-.11.334Zm-.432-.432a.676.676 0 0 0 .432.432l.11-.334a.329.329 0 0 1-.208-.208l-.334.11Zm-1.132-3.441 1.132 3.44.334-.11-1.131-3.44-.335.11Z"
          fill="url(#svgiconid_no-chats_j_18_2TP5E)"
        ></path>
        <mask id="svgiconid_no-chats_m_18_2TP5E" fill="#fff">
          <path
            pid="12"
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M25.185 86.38a8.037 8.037 0 0 1 7.158 13.995l.478 1.785-2.619-.535a8.038 8.038 0 1 1-5.017-15.245Z"
          ></path>
        </mask>
        <path
          pid="13"
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M25.185 86.38a8.037 8.037 0 0 1 7.158 13.995l.478 1.785-2.619-.535a8.038 8.038 0 1 1-5.017-15.245Z"
          fill="url(#svgiconid_no-chats_k_18_2TP5E)"
        ></path>
        <path
          pid="14"
          d="m32.343 100.375-.486.13-.084-.314.252-.205.318.389Zm.478 1.785.485-.13.21.785-.796-.162.101-.493Zm-2.619-.535-.183-.468.138-.054.146.029-.101.493Zm4.34-9.432a7.534 7.534 0 0 0-9.227-5.328l-.26-.97a8.539 8.539 0 0 1 10.458 6.038l-.97.26Zm-2.517 7.793a7.536 7.536 0 0 0 2.518-7.793l.97-.26a8.54 8.54 0 0 1-2.853 8.831l-.635-.778Zm.31 2.304-.477-1.785.97-.26.478 1.785-.97.26Zm-2.032-1.158 2.619.536-.202.985-2.619-.536.202-.985Zm-1.088.289c.276-.074.544-.163.804-.264l.366.935a8.735 8.735 0 0 1-.91.299l-.26-.97Zm-9.227-5.328a7.536 7.536 0 0 0 9.227 5.328l.26.97a8.54 8.54 0 0 1-10.457-6.038l.97-.26Zm5.327-9.228a7.534 7.534 0 0 0-5.327 9.228l-.97.26a8.539 8.539 0 0 1 6.037-10.458l.26.97Z"
          fill="url(#svgiconid_no-chats_l_18_2TP5E)"
          mask="url(#svgiconid_no-chats_m_18_2TP5E)"
        ></path>
        <path
          pid="15"
          d="M22.149 93.22a.797.797 0 1 0 1.54-.413.797.797 0 0 0-1.54.412ZM28.063 91.634a.797.797 0 1 0 1.54-.412.797.797 0 0 0-1.54.412Z"
          fill="#665252"
        ></path>
        <path
          pid="16"
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M23.769 54.96a9.112 9.112 0 0 1 8.151 13.19v4.074l-2.312-1.156a9.075 9.075 0 0 1-5.84 2.117 9.112 9.112 0 1 1 0-18.225Z"
          fill="url(#svgiconid_no-chats_n_18_2TP5E)"
        ></path>
        <path
          pid="17"
          d="M31.92 68.15h-.307v-.073l.033-.065.274.138Zm0 4.074h.307v.496l-.444-.222.137-.274Zm-2.312-1.156-.196-.236.154-.129.18.09-.137.275Zm2.966-6.996a8.805 8.805 0 0 0-8.805-8.805v-.614a9.42 9.42 0 0 1 9.42 9.42h-.615Zm-.928 3.94a8.767 8.767 0 0 0 .928-3.94h.614a9.382 9.382 0 0 1-.993 4.215l-.55-.275Zm-.033 4.212V68.15h.614v4.074h-.614Zm-1.867-1.43 2.312 1.155-.275.55-2.312-1.157.275-.549Zm-5.977 2.084a8.769 8.769 0 0 0 5.643-2.046l.393.471a9.383 9.383 0 0 1-6.036 2.189v-.614Zm-8.806-8.806a8.805 8.805 0 0 0 8.806 8.806v.614a9.42 9.42 0 0 1-9.42-9.42h.614Zm8.806-8.805a8.805 8.805 0 0 0-8.806 8.805h-.614a9.42 9.42 0 0 1 9.42-9.419v.614Z"
          fill="url(#svgiconid_no-chats_o_18_2TP5E)"
        ></path>
        <path
          pid="18"
          d="M24.252 59.74a4.695 4.695 0 1 1-4.695 4.695h4.694V59.74Z"
          fill="#FFF7EB"
        ></path>
        <path
          pid="19"
          d="M18.675 63.83h4.852v-4.852a4.853 4.853 0 0 0-4.852 4.853Z"
          fill="#FFF7EB"
        ></path>
        <path
          pid="20"
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M57.135 30.284c6.221 0 11.264 5.043 11.264 11.264a11.24 11.24 0 0 1-4.053 8.653v3.755l-3.524-1.762c-1.155.4-2.396.617-3.687.617h-5.928c-6.221 0-11.264-5.043-11.264-11.264 0-6.22 5.043-11.263 11.264-11.263h5.928Z"
          fill="url(#svgiconid_no-chats_p_18_2TP5E)"
        ></path>
        <path
          pid="21"
          d="M64.346 50.201h-.307v-.143l.11-.092.197.235Zm0 3.755h.306v.497l-.444-.222.138-.275Zm-3.524-1.762-.1-.29.122-.042.115.058-.137.274Zm7.27-10.647c0-6.05-4.906-10.956-10.957-10.956v-.614c6.39 0 11.57 5.18 11.57 11.57h-.613Zm-3.943 8.419a10.933 10.933 0 0 0 3.943-8.419h.614c0 3.573-1.62 6.768-4.164 8.89l-.393-.471Zm-.11 3.99v-3.755h.614v3.755h-.614Zm-3.08-2.036 3.524 1.761-.275.55-3.523-1.762.274-.55Zm-3.824.584c1.257 0 2.463-.211 3.587-.6l.2.58a11.554 11.554 0 0 1-3.787.634v-.614Zm-5.928 0h5.928v.614h-5.928v-.614ZM40.25 41.548c0 6.05 4.906 10.956 10.957 10.956v.614c-6.39 0-11.571-5.18-11.571-11.57h.614ZM51.207 30.59c-6.051 0-10.957 4.905-10.957 10.956h-.614c0-6.39 5.18-11.57 11.57-11.57v.614Zm5.928 0h-5.928v-.614h5.928v.614Z"
          fill="url(#svgiconid_no-chats_q_18_2TP5E)"
        ></path>
        <path
          pid="22"
          d="M48.16 38.56c0-.777.653-1.406 1.459-1.406h5.996c.806 0 1.46.63 1.46 1.406v5.776c0 .776-.654 1.406-1.46 1.406h-5.996c-.806 0-1.46-.63-1.46-1.406V38.56ZM57.836 42.087a.83.83 0 0 1 0-1.28l2.602-2.188c.57-.48 1.462-.09 1.462.64v4.376c0 .73-.892 1.12-1.462.64l-2.602-2.188Z"
          fill="#F7F8FF"
        ></path>
        <defs>
          <linearGradient
            id="svgiconid_no-chats_a_18_2TP5E"
            x1="79.254"
            y1="63.772"
            x2="79.254"
            y2="134.856"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#7EC8FF"></stop>
            <stop offset="1" stop-color="#C9E2F7"></stop>
          </linearGradient>
          <linearGradient
            id="svgiconid_no-chats_b_18_2TP5E"
            x1="80.987"
            y1="58.516"
            x2="80.987"
            y2="134.858"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#E4F1FD"></stop>
            <stop offset=".25" stop-color="#9CBDDB"></stop>
            <stop offset=".745" stop-color="#9CBDDB"></stop>
            <stop offset="1" stop-color="#70BBF1"></stop>
          </linearGradient>
          <linearGradient
            id="svgiconid_no-chats_c_18_2TP5E"
            x1="112.854"
            y1="94.346"
            x2="112.854"
            y2="142.596"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#9DD4FF"></stop>
            <stop offset="1" stop-color="#6CA2CC"></stop>
          </linearGradient>
          <linearGradient
            id="svgiconid_no-chats_d_18_2TP5E"
            x1="113.925"
            y1="88.46"
            x2="113.925"
            y2="145.716"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#73C1F9"></stop>
            <stop offset=".461" stop-color="#87CAFC"></stop>
            <stop offset="1" stop-color="#B1DBFD"></stop>
          </linearGradient>
          <linearGradient
            id="svgiconid_no-chats_e_18_2TP5E"
            x1="113.925"
            y1="88.46"
            x2="113.925"
            y2="145.716"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#DEF6FF"></stop>
            <stop offset=".357" stop-color="#AED8FF"></stop>
            <stop offset=".774" stop-color="#70BBF1"></stop>
            <stop offset="1" stop-color="#436686"></stop>
          </linearGradient>
          <linearGradient
            id="svgiconid_no-chats_g_18_2TP5E"
            x1="104.313"
            y1="67.318"
            x2="114.576"
            y2="30.764"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#FFE1BF"></stop>
            <stop offset=".381" stop-color="#F681DA"></stop>
            <stop offset="1" stop-color="#696FFF"></stop>
          </linearGradient>
          <linearGradient
            id="svgiconid_no-chats_h_18_2TP5E"
            x1="134.575"
            y1="37.824"
            x2="131.487"
            y2="81.452"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#9DA6FF"></stop>
            <stop offset=".455" stop-color="#E434C0"></stop>
            <stop offset="1" stop-color="#F16E44"></stop>
          </linearGradient>
          <linearGradient
            id="svgiconid_no-chats_j_18_2TP5E"
            x1="140.528"
            y1="55.013"
            x2="140.528"
            y2="69.502"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#FDF3E4"></stop>
            <stop offset=".25" stop-color="#FDA3B2"></stop>
            <stop offset=".745" stop-color="#CD7CC5"></stop>
            <stop offset="1" stop-color="#6F55C3"></stop>
          </linearGradient>
          <linearGradient
            id="svgiconid_no-chats_k_18_2TP5E"
            x1="25.197"
            y1="89.697"
            x2="29.804"
            y2="102.712"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#FFE590"></stop>
            <stop offset="1" stop-color="#FFBF3E"></stop>
          </linearGradient>
          <linearGradient
            id="svgiconid_no-chats_l_18_2TP5E"
            x1="25.592"
            y1="86.882"
            x2="29.896"
            y2="102.944"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#FFF0D1"></stop>
            <stop offset=".412" stop-color="#FFCB7E"></stop>
            <stop offset=".893" stop-color="#FFB24D"></stop>
            <stop offset="1" stop-color="#F7BC35"></stop>
          </linearGradient>
          <linearGradient
            id="svgiconid_no-chats_n_18_2TP5E"
            x1="23.769"
            y1="54.96"
            x2="23.769"
            y2="73.185"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#FAA17A"></stop>
            <stop offset="1" stop-color="#F7C5AD"></stop>
          </linearGradient>
          <linearGradient
            id="svgiconid_no-chats_o_18_2TP5E"
            x1="23.769"
            y1="54.96"
            x2="23.769"
            y2="73.185"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#FFDDD1"></stop>
            <stop offset=".088" stop-color="#FAA27B"></stop>
            <stop offset=".893" stop-color="#EDAB70"></stop>
            <stop offset="1" stop-color="#E3AA69"></stop>
          </linearGradient>
          <linearGradient
            id="svgiconid_no-chats_p_18_2TP5E"
            x1="54.171"
            y1="30.284"
            x2="54.171"
            y2="53.956"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#8DA9FF"></stop>
            <stop offset="1" stop-color="#B0C3FF"></stop>
          </linearGradient>
          <linearGradient
            id="svgiconid_no-chats_q_18_2TP5E"
            x1="54.171"
            y1="30.284"
            x2="54.171"
            y2="53.956"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#D1E3FF"></stop>
            <stop offset=".088" stop-color="#7BA7FA"></stop>
            <stop offset=".893" stop-color="#70A0ED"></stop>
            <stop offset="1" stop-color="#6992E3"></stop>
          </linearGradient>
          <filter
            id="svgiconid_no-chats_f_18_2TP5E"
            x="115.229"
            y="25.753"
            width="54.151"
            height="61.923"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
            <feColorMatrix
              in="SourceAlpha"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            ></feColorMatrix>
            <feOffset></feOffset>
            <feGaussianBlur stdDeviation="4.018"></feGaussianBlur>
            <feComposite in2="hardAlpha" operator="out"></feComposite>
            <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.36 0"></feColorMatrix>
            <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_15480_14909"></feBlend>
            <feBlend
              in="SourceGraphic"
              in2="effect1_dropShadow_15480_14909"
              result="shape"
            ></feBlend>
          </filter>
        </defs>
      </svg>
      <!-- 消息列表 -->
      <MessageList
        ref="messageListRef"
        :messages="currentMessages"
        :current-contact="currentChat"
      />

      <!-- 消息输入 -->
      <MessageInput
        :current-contact="currentChat"
        @send-message="sendMessage"
        @insert-emoji="insertEmoji"
        @attach-file="attachFile"
        @insert-image="insertImage"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useUserStore } from '../store/user'
import { useMessageStore } from '../store/message'
import { apiClient } from '../api'
import type { User } from '../api'
import ChatSidebar from '../components/ChatSidebar.vue'
import ChatHeader from '../components/ChatHeader.vue'
import MessageList from '../components/MessageList.vue'
import MessageInput from '../components/MessageInput.vue'

// 用户状态管理
const userStore = useUserStore()
const messageStore = useMessageStore()

// 响应式数据
const currentChatId = ref<string | null>(null)
const isLoadingUsers = ref(false)
const loadingStatus = ref('')
const messageListRef = ref<InstanceType<typeof MessageList>>()

// 聊天数据 - 从聊天联系人API获取有聊天记录的联系人
const chats = ref<
  Array<{
    id: string
    name: string
    avatar: string
    status: string
    lastMessage: string
    lastMessageTime: Date
    unreadCount: number
    user: User
  }>
>([])

// 从API加载所有用户列表
const loadChatContacts = async () => {
  try {
    isLoadingUsers.value = true
    loadingStatus.value = '正在获取用户列表...'

    const usersResponse = await apiClient.getUsers()
    if (!usersResponse.success) {
      throw new Error('获取用户列表失败')
    }

    // 过滤掉当前用户，转换为聊天列表格式
    chats.value = usersResponse.users
      .filter((user) => user.id !== userStore.currentUser.value?.id)
      .map((user) => ({
        id: user.id,
        name: user.displayName,
        avatar: getNameAvatar(user.displayName),
        status: user.isOnline ? '在线' : '离线',
        lastMessage: '点击开始聊天',
        lastMessageTime: new Date(user.lastOnlineTime),
        unreadCount: 0,
        user: user
      }))

    console.log('加载用户列表成功:', chats.value)
    loadingStatus.value = `加载完成！共 ${chats.value.length} 个联系人`
  } catch (error) {
    console.error('加载用户列表失败:', error)
    loadingStatus.value = '加载失败'
    // 如果API失败，使用默认数据
    chats.value = [
      {
        id: '689da0e4467567db78ae889d',
        name: '陈颢文',
        avatar: getNameAvatar('陈颢文'),
        status: '在线',
        lastMessage: '点击开始聊天',
        lastMessageTime: new Date(Date.now() - 1000 * 60 * 5),
        unreadCount: 0,
        user: {
          id: '689da0e4467567db78ae889d',
          username: 'chenhaowen',
          email: '<EMAIL>',
          displayName: '陈颢文',
          avatar: '/avatars/default.png',
          isOnline: true,
          lastOnlineTime: '2025-08-14T08:42:27.539Z'
        }
      }
    ]
  } finally {
    isLoadingUsers.value = false
    // 清空加载状态
    setTimeout(() => {
      loadingStatus.value = ''
    }, 1000)
  }
}

interface Message {
  id: string
  senderId: string
  senderName: string
  content: string
  timestamp: Date
}

// 计算属性
const currentChat = computed(() => {
  const chat = chats.value.find((chat) => chat.id === currentChatId.value)
  console.log(`当前聊天联系人:`, chat)
  console.log(`当前聊天ID: ${currentChatId.value}`)
  console.log(
    `联系人列表:`,
    chats.value.map((c) => ({ id: c.id, name: c.name }))
  )
  return chat
})

// 直接使用messageStore的currentMessages，并转换格式
const currentMessages = computed(() => {
  const storeMessages = messageStore.currentMessages
  console.log(`当前聊天ID: ${currentChatId.value}`)
  console.log(`messageStore中的消息数量: ${storeMessages.length}`)
  console.log(`messageStore中的消息:`, storeMessages)

  // 转换为组件需要的格式
  const transformedMessages = storeMessages.map((msg) => ({
    id: msg.id,
    senderId: msg.senderId,
    senderName: getSenderName(msg.senderId),
    content: msg.content,
    timestamp: new Date(msg.timestamp)
  }))

  console.log(`转换后的消息:`, transformedMessages)
  return transformedMessages
})

// 获取发送者姓名
const getSenderName = (senderId: string) => {
  const currentUserId = userStore.currentUser.value?.id
  console.log(`获取发送者姓名 - senderId: ${senderId}, currentUserId: ${currentUserId}`)

  if (senderId === currentUserId) {
    const name = userStore.userDisplayName.value || '我'
    console.log(`当前用户消息，显示名称: ${name}`)
    return name
  }

  // 从聊天联系人中查找
  const contact = chats.value.find((chat) => chat.user.id === senderId)
  const name = contact?.user.displayName || '未知用户'
  console.log(`其他用户消息，senderId: ${senderId}, 显示名称: ${name}`)
  return name
}

// 处理收到新消息时的联系人列表更新
const handleNewMessage = (senderId: string, content: string, timestamp: number) => {
  // 查找发送者在联系人列表中的位置
  const senderIndex = chats.value.findIndex((chat) => chat.id === senderId)

  if (senderIndex !== -1) {
    const sender = chats.value[senderIndex]

    // 更新最后消息和时间
    sender.lastMessage = content
    sender.lastMessageTime = new Date(timestamp)

    // 如果不是当前聊天用户，增加未读计数
    if (currentChatId.value !== senderId) {
      sender.unreadCount = (sender.unreadCount || 0) + 1
    }

    // 将该联系人移到列表顶部
    chats.value.splice(senderIndex, 1)
    chats.value.unshift(sender)

    console.log(`收到来自 ${sender.name} 的新消息，未读数: ${sender.unreadCount}`)
  }
}

// 清除未读计数
const clearUnreadCount = (userId: string) => {
  const contact = chats.value.find((chat) => chat.id === userId)
  if (contact && contact.unreadCount > 0) {
    contact.unreadCount = 0
    console.log(`清除用户 ${contact.name} 的未读计数`)
  }
}

// 同步messageStore中的chatSessions到联系人列表
const syncChatSessionsToContacts = () => {
  const sessions = messageStore.chatSessions

  sessions.forEach((session, userId) => {
    const contactIndex = chats.value.findIndex((chat) => chat.id === userId)

    if (contactIndex !== -1) {
      const contact = chats.value[contactIndex]

      // 更新联系人信息
      if (session.lastMessage) {
        contact.lastMessage = session.lastMessage.content
        contact.lastMessageTime = new Date(session.lastMessage.timestamp)
      }

      // 更新未读计数（如果不是当前聊天用户）
      if (currentChatId.value !== userId) {
        contact.unreadCount = session.unreadCount
      } else {
        contact.unreadCount = 0
      }

      // 如果有新消息，将联系人移到顶部
      if (session.unreadCount > 0 && currentChatId.value !== userId) {
        chats.value.splice(contactIndex, 1)
        chats.value.unshift(contact)
        console.log(`${contact.name} 有新消息，移到列表顶部`)
      }
    }
  })
}

// 组件挂载时的初始化
onMounted(async () => {
  // 检查用户是否已登录
  if (!userStore.isAuthenticated.value) {
    console.log('用户未登录，应该跳转到登录页')
    return
  }

  // 初始化WebSocket连接
  await messageStore.initWebSocket()

  // 加载聊天联系人列表
  await loadChatContacts()

  // 使用watch监听messageStore中的chatSessions变化
  watch(
    () => messageStore.chatSessions,
    (newSessions, oldSessions) => {
      console.log('检测到chatSessions变化，同步联系人列表')
      console.log('新的sessions:', Array.from(newSessions.entries()))
      syncChatSessionsToContacts()
    },
    { deep: true }
  )
})

// 工具函数
const getNameAvatar = (name: string) => {
  // 获取姓名的后两个字作为头像
  return name.length >= 2 ? name.slice(-2) : name
}

// 方法
const selectChat = async (chatId: string) => {
  console.log(`=== 选择聊天开始 ===`)
  console.log(`选择聊天: ${chatId}`)

  // 设置当前聊天ID
  currentChatId.value = chatId
  console.log(`设置currentChatId: ${currentChatId.value}`)

  // 设置messageStore的当前聊天用户
  messageStore.setCurrentChatUser(chatId)
  console.log(`设置messageStore currentChatUserId: ${messageStore.currentChatUserId}`)

  // 清除该联系人的未读计数
  clearUnreadCount(chatId)

  // 自动加载聊天历史
  console.log(`开始加载聊天历史: ${chatId}`)
  const success = await messageStore.loadChatHistory(chatId, 1, 50)
  console.log(`聊天历史加载结果: ${success}`)

  // 检查加载后的消息
  const messages = messageStore.currentMessages
  console.log(`当前消息数量: ${messages.length}`, messages)

  // 检查messages Map中的数据
  console.log(`messages Map中的所有数据:`, Array.from(messageStore.messages.entries()))
  console.log(`=== 选择聊天结束 ===`)
}

const showMoreOptions = () => {
  console.log('显示更多选项')
  // TODO: 实现更多选项功能
}

const handleLogout = () => {
  userStore.logout()
  messageStore.disconnectWebSocket()
  // 触发父组件重新渲染，显示登录页面
  window.location.reload()
}

const insertEmoji = () => {
  console.log('插入表情')
  // TODO: 实现表情选择功能
}

const attachFile = () => {
  console.log('附加文件')
  // TODO: 实现文件上传功能
}

const insertImage = () => {
  console.log('插入图片')
  // TODO: 实现图片上传功能
}

const sendMessage = async (content: string) => {
  if (!content.trim() || !currentChatId.value) {
    return
  }

  try {
    // 使用messageStore发送消息
    await messageStore.sendMessage(currentChatId.value, content)

    // 更新聊天列表中的最后消息
    const chat = chats.value.find((c) => c.id === currentChatId.value)
    if (chat) {
      chat.lastMessage = content
      chat.lastMessageTime = new Date()
    }
  } catch (error) {
    console.error('发送消息失败:', error)
  }
}
</script>
